apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: abraapi-infrastructure
type: Opaque
data:
  # Base64 encoded values - update these with your actual credentials
  # echo -n "abraapi" | base64
  POSTGRES_USER: YWJyYWFwaQ==
  # echo -n "your_secure_password" | base64
  POSTGRES_PASSWORD: eW91cl9zZWN1cmVfcGFzc3dvcmQ=
  # echo -n "abraapi_market" | base64
  POSTGRES_DB: YWJyYWFwaV9tYXJrZXQ=
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: abraapi-infrastructure
data:
  POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: abraapi-infrastructure
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-path  # K3s default storage class
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: abraapi-infrastructure
  labels:
    app: postgres
    component: database
spec:
  replicas: 1
  strategy:
    type: Recreate  # Important for databases
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
        component: database
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_PASSWORD
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_DB
        - name: POSTGRES_INITDB_ARGS
          valueFrom:
            configMapKeyRef:
              name: postgres-config
              key: POSTGRES_INITDB_ARGS
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
            - -d
            - $(POSTGRES_DB)
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 5
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
            - -d
            - $(POSTGRES_DB)
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 5
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: abraapi-infrastructure
  labels:
    app: postgres
    component: database
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app: postgres
