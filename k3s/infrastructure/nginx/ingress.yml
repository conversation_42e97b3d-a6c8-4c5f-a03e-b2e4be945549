apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: abraapi-ingress
  namespace: abraapi
  annotations:
    kubernetes.io/ingress.class: "traefik"  # K3s uses Traefik by default
    traefik.ingress.kubernetes.io/router.entrypoints: web,websecure
    traefik.ingress.kubernetes.io/router.tls: "true"
    traefik.ingress.kubernetes.io/router.tls.certresolver: "letsencrypt"
    traefik.ingress.kubernetes.io/router.middlewares: "default-redirect-https@kubernetescrd"
    # Health check configuration
    nginx.ingress.kubernetes.io/health-check-path: "/health"
    nginx.ingress.kubernetes.io/health-check-interval-seconds: "30"
    # Load balancing
    nginx.ingress.kubernetes.io/load-balance: "round_robin"
    # Rate limiting
    nginx.ingress.kubernetes.io/rate-limit-connections: "100"
    nginx.ingress.kubernetes.io/rate-limit-requests-per-second: "50"
spec:
  tls:
  - hosts:
    - abraapp.undeclab.com
    secretName: abraapi-tls
  rules:
  - host: abraapp.undeclab.com
    http:
      paths:
      # Assistant Service routes
      - path: /assistant
        pathType: Prefix
        backend:
          service:
            name: assistant-service
            port:
              number: 8080
      # Auth Service routes
      - path: /auth
        pathType: Prefix
        backend:
          service:
            name: auth-service
            port:
              number: 8080
      # Market Data Service routes
      - path: /marketdata
        pathType: Prefix
        backend:
          service:
            name: marketdata-service
            port:
              number: 8080
      - path: /api/market
        pathType: Prefix
        backend:
          service:
            name: marketdata-service
            port:
              number: 8080
      # Thread Service routes
      - path: /threads
        pathType: Prefix
        backend:
          service:
            name: thread-service
            port:
              number: 8080
      - path: /api/threads
        pathType: Prefix
        backend:
          service:
            name: thread-service
            port:
              number: 8080
      # Health check endpoint (can route to any service)
      - path: /health
        pathType: Exact
        backend:
          service:
            name: assistant-service
            port:
              number: 8080
---
# Middleware for HTTPS redirect
apiVersion: traefik.containo.us/v1alpha1
kind: Middleware
metadata:
  name: redirect-https
  namespace: default
spec:
  redirectScheme:
    scheme: https
    permanent: true
---
# Certificate for TLS
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: abraapi-tls
  namespace: abraapi
spec:
  secretName: abraapi-tls
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - abraapp.undeclab.com
---
# ClusterIssuer for Let's Encrypt
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>  # Update this with your email
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: traefik
