# AbraAPI K3s Modular Setup - Overview

## What We've Built

A complete modularization of your AbraAPI services for K3s deployment. Each service now has:

### ✅ Individual Docker Compose Files
- `docker-compose/assistant-service.yml` - Assistant service only
- `docker-compose/auth-service.yml` - Auth service only  
- `docker-compose/marketdata-service.yml` - Market data + PostgreSQL + Redis
- `docker-compose/thread-service.yml` - Thread service + PostgreSQL

### ✅ Individual K3s Manifests
- `services/assistant-service/deployment.yml` - Complete K8s deployment
- `services/auth-service/deployment.yml` - Complete K8s deployment
- `services/marketdata-service/deployment.yml` - Complete K8s deployment
- `services/thread-service/deployment.yml` - Complete K8s deployment

### ✅ Shared Infrastructure
- `infrastructure/postgres/postgres.yml` - PostgreSQL for all services
- `infrastructure/redis/redis.yml` - Redis for caching
- `infrastructure/nginx/ingress.yml` - Ingress with SSL

### ✅ Individual CI/CD Pipelines
- `ci-cd/simple-assistant-service.yml` - Deploys only when AssistantService changes
- `ci-cd/simple-auth-service.yml` - Deploys only when AuthService changes
- `ci-cd/simple-marketdata-service.yml` - Deploys only when MarketDataService changes
- `ci-cd/simple-thread-service.yml` - Deploys only when ThreadService changes

## Key Benefits

### 🔄 Independent Development
Each service can be developed, tested, and deployed independently:
```bash
# Work on assistant service only
cd k3s/docker-compose
docker-compose -f assistant-service.yml up --build
```

### 🚀 Independent Deployment
Deploy only what changed:
```bash
# Deploy only assistant service
kubectl apply -f services/assistant-service/deployment.yml
```

### 📈 Independent Scaling
Scale services based on their specific needs:
```bash
# Scale market data service higher (more CPU intensive)
kubectl scale deployment marketdata-service --replicas=5 -n abraapi

# Scale assistant service normally
kubectl scale deployment assistant-service --replicas=2 -n abraapi
```

### 🔧 Independent CI/CD
Each service has its own GitHub Actions workflow that only triggers when that specific service changes.

## Service Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Assistant       │    │ Auth            │    │ MarketData      │    │ Thread          │
│ Service         │    │ Service         │    │ Service         │    │ Service         │
│                 │    │                 │    │                 │    │                 │
│ Port: 8080      │    │ Port: 8080      │    │ Port: 8080      │    │ Port: 8080      │
│ Deps: None      │    │ Deps: None      │    │ Deps: PG, Redis │    │ Deps: PG        │
│ External: Ollama│    │ External: Supa  │    │ External: APIs  │    │ External: Supa  │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │                       │
                                                       ▼                       ▼
                                              ┌─────────────────┐    ┌─────────────────┐
                                              │ PostgreSQL      │    │ Redis           │
                                              │ Port: 5432      │    │ Port: 6379      │
                                              │ Namespace:      │    │ Namespace:      │
                                              │ infrastructure  │    │ infrastructure  │
                                              └─────────────────┘    └─────────────────┘
```

## How to Use

### 1. Setup (One Time)
```bash
# Install K3s
curl -sfL https://get.k3s.io | sh -
export KUBECONFIG=/etc/rancher/k3s/k3s.yaml

# Create namespaces
kubectl create namespace abraapi
kubectl create namespace abraapi-infrastructure

# Deploy infrastructure
kubectl apply -f infrastructure/

# Deploy all services
kubectl apply -f services/
```

### 2. Development Workflow
```bash
# Work on a specific service locally
cd k3s/docker-compose
docker-compose -f assistant-service.yml up --build

# Deploy changes to K3s
kubectl apply -f services/assistant-service/deployment.yml
```

### 3. CI/CD Setup
Copy the workflow files to your `.github/workflows/` directory:
```bash
cp k3s/ci-cd/simple-*.yml .github/workflows/
```

### 4. Management
Use simple kubectl commands (see `COMMANDS.md` for full list):
```bash
# Check status
kubectl get pods -n abraapi

# View logs
kubectl logs -l app=assistant-service -n abraapi

# Scale
kubectl scale deployment assistant-service --replicas=3 -n abraapi

# Restart
kubectl rollout restart deployment/assistant-service -n abraapi
```

## What's Different from Before

### Before (Monolithic)
- One big `docker-compose.yml` with all services
- One CI/CD pipeline that deploys everything
- Changes to any service redeploys everything
- All services scale together

### Now (Modular)
- Individual Docker Compose files for each service
- Individual CI/CD pipelines for each service  
- Changes to a service only redeploys that service
- Each service scales independently
- Shared infrastructure (PostgreSQL, Redis) used by multiple services

## Next Steps

1. **Copy CI/CD workflows** to `.github/workflows/`
2. **Update secrets** in the K8s manifests with your actual values
3. **Test locally** with the individual Docker Compose files
4. **Deploy to K3s** using the simple setup guide
5. **Configure ingress** with your domain name

No complex scripts, just simple commands and clear manifests!
