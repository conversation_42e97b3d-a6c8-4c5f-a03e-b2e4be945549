# AbraAPI K3s Setup Guide

This guide will help you set up the modularized AbraAPI services on K3s.

## Prerequisites

- Linux server with at least 2GB RAM and 10GB disk space
- Root or sudo access
- Internet connection for downloading components

## Quick Start

### 1. Setup K3s Cluster

```bash
# Navigate to the k3s directory
cd k3s

# Run the setup script
./scripts/setup-k3s.sh
```

This script will:
- Install K3s with optimized configuration
- Install Helm package manager
- Install cert-manager for SSL certificates
- Install Traefik ingress controller
- Create necessary namespaces
- Deploy PostgreSQL and Redis infrastructure

### 2. Configure Secrets

Before deploying services, you need to update the Kubernetes secrets with your actual credentials:

```bash
# Edit the secret files with your actual base64-encoded values
# You can encode values using: echo -n "your_value" | base64

# Assistant Service secrets
kubectl edit secret assistant-service-secret -n abraapi

# Auth Service secrets  
kubectl edit secret auth-service-secret -n abraapi

# MarketData Service secrets
kubectl edit secret marketdata-service-secret -n abraapi

# Thread Service secrets
kubectl edit secret thread-service-secret -n abraapi
```

### 3. Deploy Services

Deploy services individually:

```bash
# Deploy assistant service
./scripts/deploy-service.sh assistant-service --build --status

# Deploy auth service
./scripts/deploy-service.sh auth-service --build --status

# Deploy market data service (requires PostgreSQL and Redis)
./scripts/deploy-service.sh marketdata-service --build --status

# Deploy thread service (requires PostgreSQL)
./scripts/deploy-service.sh thread-service --build --status
```

### 4. Configure Ingress

Update the ingress configuration with your domain:

```bash
# Edit the ingress file
kubectl edit ingress abraapi-ingress -n abraapi

# Update the domain name and email for Let's Encrypt
kubectl edit clusterissuer letsencrypt-prod
```

## Development Workflow

### Local Development

Each service can be developed independently using Docker Compose:

```bash
# Navigate to docker-compose directory
cd docker-compose

# Copy environment template
cp .env.example .env
# Edit .env with your configuration

# Develop assistant service
docker-compose -f assistant-service.yml up --build

# Develop auth service  
docker-compose -f auth-service.yml up --build

# Develop market data service (includes PostgreSQL and Redis)
docker-compose -f marketdata-service.yml up --build

# Develop thread service (includes PostgreSQL)
docker-compose -f thread-service.yml up --build
```

### Testing Changes

1. Make changes to your service code
2. Test locally with Docker Compose
3. Build and deploy to K3s:

```bash
./scripts/deploy-service.sh <service-name> --build --status
```

### CI/CD Integration

Each service has its own GitHub Actions workflow in the `ci-cd/` directory. To use them:

1. Copy the workflow files to `.github/workflows/`:

```bash
# Copy individual service workflows
cp k3s/ci-cd/assistant-service.yml .github/workflows/
cp k3s/ci-cd/auth-service.yml .github/workflows/
cp k3s/ci-cd/marketdata-service.yml .github/workflows/
cp k3s/ci-cd/thread-service.yml .github/workflows/
```

2. Configure GitHub secrets:
   - `KUBECONFIG`: Base64-encoded kubeconfig file
   - Service-specific secrets (database credentials, API keys, etc.)

3. Push changes to trigger automatic deployment

## Cluster Management

Use the management script for common operations:

```bash
# Show cluster status
./scripts/manage-cluster.sh status

# Show service logs
./scripts/manage-cluster.sh logs assistant-service

# Scale a service
./scripts/manage-cluster.sh scale auth-service 3

# Restart a service
./scripts/manage-cluster.sh restart marketdata-service

# Monitor cluster resources
./scripts/manage-cluster.sh monitor

# Backup cluster configuration
./scripts/manage-cluster.sh backup

# Clean up unused resources
./scripts/manage-cluster.sh cleanup
```

## Service Architecture

### Assistant Service
- **Dependencies**: None (uses external Ollama API)
- **Port**: 8080
- **Health Check**: `/health`
- **Scaling**: 2-10 replicas

### Auth Service
- **Dependencies**: None (uses external Supabase)
- **Port**: 8080  
- **Health Check**: `/health`
- **Scaling**: 2-10 replicas

### MarketData Service
- **Dependencies**: PostgreSQL, Redis
- **Port**: 8080
- **Health Check**: `/health`
- **Scaling**: 2-15 replicas (higher for market data processing)

### Thread Service
- **Dependencies**: PostgreSQL (shared with MarketData)
- **Port**: 8080
- **Health Check**: `/health/ready`
- **Scaling**: 2-10 replicas

## Networking

Services communicate within the cluster using Kubernetes DNS:

- `assistant-service.abraapi.svc.cluster.local:8080`
- `auth-service.abraapi.svc.cluster.local:8080`
- `marketdata-service.abraapi.svc.cluster.local:8080`
- `thread-service.abraapi.svc.cluster.local:8080`

Infrastructure services:
- `postgres.abraapi-infrastructure.svc.cluster.local:5432`
- `redis.abraapi-infrastructure.svc.cluster.local:6379`

## Monitoring and Observability

### Built-in Monitoring

- Health checks for all services
- Horizontal Pod Autoscaling (HPA) based on CPU/memory
- Resource limits and requests
- Liveness and readiness probes

### Accessing Logs

```bash
# View logs for a specific service
kubectl logs -n abraapi -l app=assistant-service --tail=100

# Follow logs in real-time
kubectl logs -n abraapi -l app=assistant-service -f

# View logs for infrastructure
kubectl logs -n abraapi-infrastructure -l app=postgres --tail=100
```

### Metrics

K3s includes basic metrics. For advanced monitoring, consider installing:

- Prometheus for metrics collection
- Grafana for visualization
- Jaeger for distributed tracing

## Troubleshooting

### Common Issues

1. **Pods not starting**: Check resource limits and node capacity
2. **Image pull errors**: Ensure images are built and available
3. **Database connection issues**: Verify PostgreSQL is running and accessible
4. **Health check failures**: Check service startup time and health endpoints

### Debugging Commands

```bash
# Check pod status
kubectl get pods -n abraapi -o wide

# Describe a problematic pod
kubectl describe pod <pod-name> -n abraapi

# Check events
kubectl get events -n abraapi --sort-by='.lastTimestamp'

# Check service endpoints
kubectl get endpoints -n abraapi

# Test service connectivity
kubectl run test-pod --rm -i --restart=Never --image=curlimages/curl -- \
  curl -v http://assistant-service.abraapi.svc.cluster.local:8080/health
```

## Security Considerations

- All services run as non-root users
- Secrets are stored in Kubernetes secrets (base64 encoded)
- Network policies can be added for additional isolation
- TLS termination at ingress level
- Regular security updates for base images

## Backup and Recovery

### Automated Backups

```bash
# Create a backup
./scripts/manage-cluster.sh backup ./backups/manual-backup

# Backup includes:
# - All Kubernetes resources
# - ConfigMaps and Secrets
# - Persistent Volume configurations
# - Ingress configurations
```

### Database Backups

PostgreSQL data is persisted in Kubernetes Persistent Volumes. For production:

1. Set up regular database backups
2. Consider using external managed databases
3. Implement point-in-time recovery

## Production Considerations

1. **Resource Limits**: Adjust CPU/memory limits based on load testing
2. **Scaling**: Configure HPA thresholds based on your traffic patterns
3. **Storage**: Use appropriate storage classes for your environment
4. **Networking**: Configure network policies for security
5. **Monitoring**: Implement comprehensive monitoring and alerting
6. **Backups**: Set up automated backup procedures
7. **Updates**: Plan for rolling updates and rollback procedures

## Migration from Docker Compose

To migrate from the existing Docker Compose setup:

1. Export current data from PostgreSQL and Redis
2. Set up K3s cluster using this guide
3. Import data to the new infrastructure
4. Update DNS/load balancer to point to K3s ingress
5. Monitor and validate the migration

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review Kubernetes and K3s documentation
3. Check service logs and events
4. Use the management script for cluster diagnostics
