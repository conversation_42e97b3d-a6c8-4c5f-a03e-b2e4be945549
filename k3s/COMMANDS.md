# Simple K3s Management Commands

## Basic Operations

### Check Status
```bash
kubectl get pods -n abraapi
kubectl get pods -n abraapi-infrastructure
kubectl get services -n abraapi
```

### View Logs
```bash
kubectl logs -l app=assistant-service -n abraapi
kubectl logs -l app=auth-service -n abraapi
kubectl logs -l app=marketdata-service -n abraapi
kubectl logs -l app=thread-service -n abraapi
```

### Scale Services
```bash
kubectl scale deployment assistant-service --replicas=3 -n abraapi
kubectl scale deployment auth-service --replicas=3 -n abraapi
kubectl scale deployment marketdata-service --replicas=5 -n abraapi
kubectl scale deployment thread-service --replicas=3 -n abraapi
```

### Restart Services
```bash
kubectl rollout restart deployment/assistant-service -n abraapi
kubectl rollout restart deployment/auth-service -n abraapi
kubectl rollout restart deployment/marketdata-service -n abraapi
kubectl rollout restart deployment/thread-service -n abraapi
```

### Delete Services
```bash
kubectl delete deployment assistant-service -n abraapi
kubectl delete deployment auth-service -n abraapi
kubectl delete deployment marketdata-service -n abraapi
kubectl delete deployment thread-service -n abraapi
```

## Development

### Build and Update Image
```bash
# Build new image
docker build -t assistant-service:latest AssistantService/

# Import to K3s
sudo k3s ctr images import <(docker save assistant-service:latest)

# Update deployment
kubectl set image deployment/assistant-service assistant-service=assistant-service:latest -n abraapi

# Wait for rollout
kubectl rollout status deployment/assistant-service -n abraapi
```

### Local Development
```bash
cd k3s/docker-compose
docker-compose -f assistant-service.yml up --build
docker-compose -f auth-service.yml up --build
docker-compose -f marketdata-service.yml up --build
docker-compose -f thread-service.yml up --build
```

## Troubleshooting

### Check Pod Details
```bash
kubectl describe pod <pod-name> -n abraapi
kubectl get events -n abraapi --sort-by='.lastTimestamp'
```

### Test Service Connectivity
```bash
kubectl run test --rm -i --restart=Never --image=curlimages/curl -- \
  curl http://assistant-service.abraapi.svc.cluster.local:8080/health
```

### Access Pod Shell
```bash
kubectl exec -it <pod-name> -n abraapi -- /bin/sh
```

## Backup

### Export Resources
```bash
kubectl get all -n abraapi -o yaml > abraapi-backup.yaml
kubectl get all -n abraapi-infrastructure -o yaml > infrastructure-backup.yaml
```

### Backup Secrets
```bash
kubectl get secrets -n abraapi -o yaml > secrets-backup.yaml
```

## Cleanup

### Remove Failed Pods
```bash
kubectl delete pods --field-selector=status.phase=Failed -n abraapi
kubectl delete pods --field-selector=status.phase=Succeeded -n abraapi
```

### Clean Up Everything
```bash
kubectl delete namespace abraapi
kubectl delete namespace abraapi-infrastructure
```

That's it - simple commands, no complex scripts!
