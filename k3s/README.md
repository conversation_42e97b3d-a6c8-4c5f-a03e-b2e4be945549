# K3s Modular Deployment - Simple Setup

This directory contains the modularized K3s deployment configuration for the AbraAPI microservices.

**No complex scripts needed** - just simple kubectl commands and Docker Compose files.

## Structure

```
k3s/
├── services/                    # Individual service K8s manifests
│   ├── assistant-service/       # Assistant service deployment
│   ├── auth-service/           # Auth service deployment
│   ├── marketdata-service/     # Market data service deployment
│   └── thread-service/         # Thread service deployment
├── infrastructure/             # Shared infrastructure
│   ├── postgres/              # PostgreSQL deployment
│   ├── redis/                 # Redis deployment
│   └── nginx/                 # Ingress configuration
├── docker-compose/            # Individual Docker Compose files
│   ├── assistant-service.yml  # Assistant service local dev
│   ├── auth-service.yml       # Auth service local dev
│   ├── marketdata-service.yml # Market data service local dev
│   └── thread-service.yml     # Thread service local dev
├── ci-cd/                     # Simple GitHub Actions workflows
│   ├── simple-assistant-service.yml
│   ├── simple-auth-service.yml
│   ├── simple-marketdata-service.yml
│   └── simple-thread-service.yml
├── SIMPLE_SETUP.md            # Step-by-step setup guide
└── COMMANDS.md                # Common management commands
```

## Services Overview

### Assistant Service
- **Dependencies**: None (external Ollama API)
- **Port**: 8080
- **Health Check**: `/health`

### Auth Service
- **Dependencies**: None (external Supabase)
- **Port**: 8080
- **Health Check**: `/health`

### Market Data Service
- **Dependencies**: PostgreSQL, Redis
- **Port**: 8080
- **Health Check**: `/health`

### Thread Service
- **Dependencies**: PostgreSQL (shared with MarketData)
- **Port**: 8080
- **Health Check**: `/health/ready`

## Quick Start

1. **Setup K3s**:
   ```bash
   curl -sfL https://get.k3s.io | sh -
   export KUBECONFIG=/etc/rancher/k3s/k3s.yaml
   ```

2. **Deploy everything**:
   ```bash
   kubectl create namespace abraapi
   kubectl create namespace abraapi-infrastructure
   kubectl apply -f infrastructure/
   kubectl apply -f services/
   ```

3. **Check status**:
   ```bash
   kubectl get pods -n abraapi
   ```

## Development

Each service can be developed independently:

```bash
# Local development
cd docker-compose/
cp .env.example .env
docker-compose -f assistant-service.yml up --build

# Deploy to K3s
kubectl apply -f services/assistant-service/deployment.yml
```

See `SIMPLE_SETUP.md` for detailed instructions and `COMMANDS.md` for common operations.
