name: Auth Service Deploy

on:
  push:
    branches: [main]
    paths: ['AuthService/**']
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Build Docker image
      run: |
        docker build -t auth-service:${{ github.sha }} AuthService/
        docker tag auth-service:${{ github.sha }} auth-service:latest
    
    - name: Deploy to K3s
      run: |
        # Setup kubectl
        mkdir -p ~/.kube
        echo "${{ secrets.KUBECONFIG }}" | base64 -d > ~/.kube/config
        
        # Update image in deployment
        kubectl set image deployment/auth-service auth-service=auth-service:${{ github.sha }} -n abraapi
        
        # Wait for rollout
        kubectl rollout status deployment/auth-service -n abraapi --timeout=600s
