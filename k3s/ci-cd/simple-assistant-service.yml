name: Assistant Service Deploy

on:
  push:
    branches: [main]
    paths: ['AssistantService/**']
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Build Docker image
      run: |
        docker build -t assistant-service:${{ github.sha }} AssistantService/
        docker tag assistant-service:${{ github.sha }} assistant-service:latest
    
    - name: Deploy to K3s
      run: |
        # Setup kubectl
        mkdir -p ~/.kube
        echo "${{ secrets.KUBECONFIG }}" | base64 -d > ~/.kube/config
        
        # Update image in deployment
        kubectl set image deployment/assistant-service assistant-service=assistant-service:${{ github.sha }} -n abraapi
        
        # Wait for rollout
        kubectl rollout status deployment/assistant-service -n abraapi --timeout=600s
