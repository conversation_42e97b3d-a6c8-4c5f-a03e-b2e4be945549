name: MarketData Service Deploy

on:
  push:
    branches: [main]
    paths: ['MarketDataService/**']
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Build Docker image
      run: |
        docker build -t marketdata-service:${{ github.sha }} MarketDataService/
        docker tag marketdata-service:${{ github.sha }} marketdata-service:latest
    
    - name: Deploy to K3s
      run: |
        # Setup kubectl
        mkdir -p ~/.kube
        echo "${{ secrets.KUBECONFIG }}" | base64 -d > ~/.kube/config
        
        # Update image in deployment
        kubectl set image deployment/marketdata-service marketdata-service=marketdata-service:${{ github.sha }} -n abraapi
        
        # Wait for rollout
        kubectl rollout status deployment/marketdata-service -n abraapi --timeout=600s
