name: Thread Service Deploy

on:
  push:
    branches: [main]
    paths: ['ThreadService/**']
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Build Docker image
      run: |
        docker build -t thread-service:${{ github.sha }} ThreadService/
        docker tag thread-service:${{ github.sha }} thread-service:latest
    
    - name: Deploy to K3s
      run: |
        # Setup kubectl
        mkdir -p ~/.kube
        echo "${{ secrets.KUBECONFIG }}" | base64 -d > ~/.kube/config
        
        # Update image in deployment
        kubectl set image deployment/thread-service thread-service=thread-service:${{ github.sha }} -n abraapi
        
        # Wait for rollout
        kubectl rollout status deployment/thread-service -n abraapi --timeout=600s
