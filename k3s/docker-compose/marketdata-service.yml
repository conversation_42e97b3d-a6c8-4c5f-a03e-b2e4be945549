version: '3.8'

services:
  # Database services - required for MarketData service
  postgres:
    image: postgres:15-alpine
    container_name: postgres-marketdata
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-abraapi}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_DB: ${POSTGRES_DB:-abraapi_market}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - pgdata_market:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-abraapi} -d ${POSTGRES_DB:-abraapi_market}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    labels:
      - "service=postgres"
      - "component=database"

  redis:
    image: redis:7-alpine
    container_name: redis-marketdata
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    labels:
      - "service=redis"
      - "component=cache"

  marketdata-service:
    container_name: marketdata-service
    restart: unless-stopped
    build:
      context: ../../MarketDataService
      dockerfile: Dockerfile
      target: runtime
      args:
        BUILDKIT_INLINE_CACHE: 1
    ports:
      - "8082:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Development}
      - ASPNETCORE_URLS=http://+:8080
      - SUPABASE_CONNECTION_STRING=${SUPABASE_CONNECTION_STRING}
      - Redis__ConnectionString=${REDIS_CONNECTION_STRING:-redis:6379}
      - FINNHUB_API_KEY=${FINNHUB_API_KEY}
      - POLYGON_API_KEY=${POLYGON_API_KEY}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 45s
      timeout: 30s
      retries: 5
      start_period: 180s
    labels:
      - "service=marketdata-service"
      - "version=1.0"
      - "environment=${ASPNETCORE_ENVIRONMENT:-development}"

volumes:
  pgdata_market:
    name: pgdata_market
  redis_data:
    name: redis_data

networks:
  default:
    name: marketdata-network
    driver: bridge

# Environment variables for local development
# Create a .env file in this directory with:
# ASPNETCORE_ENVIRONMENT=Development
# POSTGRES_USER=abraapi
# POSTGRES_PASSWORD=password
# POSTGRES_DB=abraapi_market
# REDIS_CONNECTION_STRING=redis:6379
# SUPABASE_CONNECTION_STRING=your_connection_string
# FINNHUB_API_KEY=your_finnhub_key
# POLYGON_API_KEY=your_polygon_key
# SUPABASE_URL=your_supabase_url
# SUPABASE_JWT_SECRET=your_jwt_secret
