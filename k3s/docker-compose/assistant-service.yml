version: '3.8'

services:
  assistant-service:
    container_name: assistant-service
    restart: unless-stopped
    build:
      context: ../../AssistantService
      dockerfile: Dockerfile
      target: runtime
      args:
        BUILDKIT_INLINE_CACHE: 1
    ports:
      - "8080:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Development}
      - ASPNETCORE_URLS=http://+:8080
      - OLLAMA_URL=${OLLAMA_URL:-http://assistant.undeclab.com/api/generate}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 45s
      timeout: 30s
      retries: 5
      start_period: 180s
    labels:
      - "service=assistant-service"
      - "version=1.0"
      - "environment=${ASPNETCORE_ENVIRONMENT:-development}"

networks:
  default:
    name: assistant-network
    driver: bridge

# Environment variables for local development
# Create a .env file in this directory with:
# ASPNETCORE_ENVIRONMENT=Development
# OLLAMA_URL=http://assistant.undeclab.com/api/generate
