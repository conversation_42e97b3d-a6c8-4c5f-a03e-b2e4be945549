# Environment Configuration for AbraAPI Services
# Copy this file to .env and update with your actual values

# =============================================================================
# GENERAL CONFIGURATION
# =============================================================================
ASPNETCORE_ENVIRONMENT=Development

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
POSTGRES_USER=abraapi
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=abraapi_market

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_CONNECTION_STRING=redis:6379

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
SUPABASE_JWT_SECRET=your_supabase_jwt_secret_here
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_PROJECT_ID=your_project_id_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_CONNECTION_STRING=postgresql://postgres:<EMAIL>:5432/postgres

# =============================================================================
# API KEYS
# =============================================================================
FINNHUB_API_KEY=your_finnhub_api_key_here
POLYGON_API_KEY=your_polygon_api_key_here

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
OLLAMA_URL=http://assistant.undeclab.com/api/generate

# =============================================================================
# SERVICE-SPECIFIC CONFIGURATION
# =============================================================================

# Assistant Service
# - Uses OLLAMA_URL for AI model integration
# - No database dependencies

# Auth Service  
# - Uses Supabase for authentication
# - No database dependencies

# MarketData Service
# - Requires PostgreSQL and Redis
# - Uses external API keys for market data

# Thread Service
# - Requires PostgreSQL (shared with MarketData)
# - Uses Supabase for additional features
