version: '3.8'

services:
  auth-service:
    container_name: auth-service
    restart: unless-stopped
    build:
      context: ../../AuthService
      dockerfile: Dockerfile
      target: runtime
      args:
        BUILDKIT_INLINE_CACHE: 1
    ports:
      - "8081:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Development}
      - ASPNETCORE_URLS=http://+:8080
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_PROJECT_ID=${SUPABASE_PROJECT_ID}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 45s
      timeout: 30s
      retries: 5
      start_period: 180s
    labels:
      - "service=auth-service"
      - "version=1.0"
      - "environment=${ASPNETCORE_ENVIRONMENT:-development}"

networks:
  default:
    name: auth-network
    driver: bridge

# Environment variables for local development
# Create a .env file in this directory with:
# ASPNETCORE_ENVIRONMENT=Development
# SUPABASE_JWT_SECRET=your_jwt_secret
# SUPABASE_URL=your_supabase_url
# SUPABASE_PROJECT_ID=your_project_id
# SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
# SUPABASE_ANON_KEY=your_anon_key
