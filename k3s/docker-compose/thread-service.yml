version: '3.8'

services:
  # PostgreSQL - shared with MarketData service
  postgres:
    image: postgres:15-alpine
    container_name: postgres-thread
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-abraapi}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_DB: ${POSTGRES_DB:-abraapi_market}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - pgdata_thread:/var/lib/postgresql/data
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-abraapi} -d ${POSTGRES_DB:-abraapi_market}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    labels:
      - "service=postgres"
      - "component=database"

  thread-service:
    container_name: thread-service
    restart: unless-stopped
    build:
      context: ../../ThreadService
      dockerfile: Dockerfile
      target: runtime
      args:
        BUILDKIT_INLINE_CACHE: 1
    ports:
      - "8083:8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Development}
      - ASPNETCORE_URLS=http://+:8080
      - SUPABASE_CONNECTION_STRING=${SUPABASE_CONNECTION_STRING}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health/ready"]
      interval: 45s
      timeout: 30s
      retries: 5
      start_period: 300s
    labels:
      - "service=thread-service"
      - "version=1.0"
      - "environment=${ASPNETCORE_ENVIRONMENT:-development}"

volumes:
  pgdata_thread:
    name: pgdata_thread

networks:
  default:
    name: thread-network
    driver: bridge

# Environment variables for local development
# Create a .env file in this directory with:
# ASPNETCORE_ENVIRONMENT=Development
# POSTGRES_USER=abraapi
# POSTGRES_PASSWORD=password
# POSTGRES_DB=abraapi_market
# SUPABASE_CONNECTION_STRING=your_connection_string
# SUPABASE_URL=your_supabase_url
# SUPABASE_JWT_SECRET=your_jwt_secret
