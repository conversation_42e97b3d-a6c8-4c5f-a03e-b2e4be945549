apiVersion: v1
kind: Secret
metadata:
  name: thread-service-secret
  namespace: abraapi
type: Opaque
data:
  # Base64 encoded values - update these with your actual credentials
  # echo -n "your_supabase_connection_string" | base64
  SUPABASE_CONNECTION_STRING: eW91cl9zdXBhYmFzZV9jb25uZWN0aW9uX3N0cmluZw==
  # echo -n "your_supabase_url" | base64
  SUPABASE_URL: eW91cl9zdXBhYmFzZV91cmw=
  # echo -n "your_supabase_jwt_secret" | base64
  SUPABASE_JWT_SECRET: eW91cl9zdXBhYmFzZV9qd3Rfc2VjcmV0
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: thread-service-config
  namespace: abraapi
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  ASPNETCORE_URLS: "http://+:8080"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: thread-service
  namespace: abraapi
  labels:
    app: thread-service
    service: thread
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: thread-service
  template:
    metadata:
      labels:
        app: thread-service
        service: thread
        version: v1
    spec:
      containers:
      - name: thread-service
        image: thread-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: thread-service-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: thread-service-config
              key: ASPNETCORE_URLS
        - name: SUPABASE_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: thread-service-secret
              key: SUPABASE_CONNECTION_STRING
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: thread-service-secret
              key: SUPABASE_URL
        - name: SUPABASE_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: thread-service-secret
              key: SUPABASE_JWT_SECRET
        livenessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 300
          periodSeconds: 45
          timeoutSeconds: 30
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
---
apiVersion: v1
kind: Service
metadata:
  name: thread-service
  namespace: abraapi
  labels:
    app: thread-service
    service: thread
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: thread-service
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: thread-service-hpa
  namespace: abraapi
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: thread-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
