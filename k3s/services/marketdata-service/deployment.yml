apiVersion: v1
kind: Secret
metadata:
  name: marketdata-service-secret
  namespace: abraapi
type: Opaque
data:
  # Base64 encoded values - update these with your actual credentials
  # echo -n "your_supabase_connection_string" | base64
  SUPABASE_CONNECTION_STRING: eW91cl9zdXBhYmFzZV9jb25uZWN0aW9uX3N0cmluZw==
  # echo -n "your_finnhub_api_key" | base64
  FINNHUB_API_KEY: eW91cl9maW5uaHViX2FwaV9rZXk=
  # echo -n "your_polygon_api_key" | base64
  POLYGON_API_KEY: eW91cl9wb2x5Z29uX2FwaV9rZXk=
  # echo -n "your_supabase_url" | base64
  SUPABASE_URL: eW91cl9zdXBhYmFzZV91cmw=
  # echo -n "your_supabase_jwt_secret" | base64
  SUPABASE_JWT_SECRET: eW91cl9zdXBhYmFzZV9qd3Rfc2VjcmV0
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: marketdata-service-config
  namespace: abraapi
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  ASPNETCORE_URLS: "http://+:8080"
  Redis__ConnectionString: "redis.abraapi-infrastructure.svc.cluster.local:6379"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: marketdata-service
  namespace: abraapi
  labels:
    app: marketdata-service
    service: marketdata
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: marketdata-service
  template:
    metadata:
      labels:
        app: marketdata-service
        service: marketdata
        version: v1
    spec:
      containers:
      - name: marketdata-service
        image: marketdata-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: marketdata-service-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: marketdata-service-config
              key: ASPNETCORE_URLS
        - name: Redis__ConnectionString
          valueFrom:
            configMapKeyRef:
              name: marketdata-service-config
              key: Redis__ConnectionString
        - name: SUPABASE_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: marketdata-service-secret
              key: SUPABASE_CONNECTION_STRING
        - name: FINNHUB_API_KEY
          valueFrom:
            secretKeyRef:
              name: marketdata-service-secret
              key: FINNHUB_API_KEY
        - name: POLYGON_API_KEY
          valueFrom:
            secretKeyRef:
              name: marketdata-service-secret
              key: POLYGON_API_KEY
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: marketdata-service-secret
              key: SUPABASE_URL
        - name: SUPABASE_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: marketdata-service-secret
              key: SUPABASE_JWT_SECRET
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 180
          periodSeconds: 45
          timeoutSeconds: 30
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
---
apiVersion: v1
kind: Service
metadata:
  name: marketdata-service
  namespace: abraapi
  labels:
    app: marketdata-service
    service: marketdata
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: marketdata-service
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: marketdata-service-hpa
  namespace: abraapi
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: marketdata-service
  minReplicas: 2
  maxReplicas: 15
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
