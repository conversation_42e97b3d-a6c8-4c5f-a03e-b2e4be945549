apiVersion: v1
kind: Secret
metadata:
  name: auth-service-secret
  namespace: abraapi
type: Opaque
data:
  # Base64 encoded values - update these with your actual credentials
  # echo -n "your_supabase_jwt_secret" | base64
  SUPABASE_JWT_SECRET: eW91cl9zdXBhYmFzZV9qd3Rfc2VjcmV0
  # echo -n "your_supabase_url" | base64
  SUPABASE_URL: eW91cl9zdXBhYmFzZV91cmw=
  # echo -n "your_project_id" | base64
  SUPABASE_PROJECT_ID: eW91cl9wcm9qZWN0X2lk
  # echo -n "your_service_role_key" | base64
  SUPABASE_SERVICE_ROLE_KEY: eW91cl9zZXJ2aWNlX3JvbGVfa2V5
  # echo -n "your_anon_key" | base64
  SUPABASE_ANON_KEY: eW91cl9hbm9uX2tleQ==
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: auth-service-config
  namespace: abraapi
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  ASPNETCORE_URLS: "http://+:8080"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  namespace: abraapi
  labels:
    app: auth-service
    service: auth
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
        service: auth
        version: v1
    spec:
      containers:
      - name: auth-service
        image: auth-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: auth-service-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: auth-service-config
              key: ASPNETCORE_URLS
        - name: SUPABASE_JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_JWT_SECRET
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_URL
        - name: SUPABASE_PROJECT_ID
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_PROJECT_ID
        - name: SUPABASE_SERVICE_ROLE_KEY
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_SERVICE_ROLE_KEY
        - name: SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: auth-service-secret
              key: SUPABASE_ANON_KEY
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 180
          periodSeconds: 45
          timeoutSeconds: 30
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
---
apiVersion: v1
kind: Service
metadata:
  name: auth-service
  namespace: abraapi
  labels:
    app: auth-service
    service: auth
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: auth-service
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: auth-service-hpa
  namespace: abraapi
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: auth-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
