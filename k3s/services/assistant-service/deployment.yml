apiVersion: v1
kind: Secret
metadata:
  name: assistant-service-secret
  namespace: abraapi
type: Opaque
data:
  # Base64 encoded values - update these with your actual credentials
  # echo -n "http://assistant.undeclab.com/api/generate" | base64
  OLLAMA_URL: aHR0cDovL2Fzc2lzdGFudC51bmRlY2xhYi5jb20vYXBpL2dlbmVyYXRl
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: assistant-service-config
  namespace: abraapi
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  ASPNETCORE_URLS: "http://+:8080"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: assistant-service
  namespace: abraapi
  labels:
    app: assistant-service
    service: assistant
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: assistant-service
  template:
    metadata:
      labels:
        app: assistant-service
        service: assistant
        version: v1
    spec:
      containers:
      - name: assistant-service
        image: assistant-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ASPNETCORE_ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: assistant-service-config
              key: ASPNETCORE_ENVIRONMENT
        - name: ASPNETCORE_URLS
          valueFrom:
            configMapKeyRef:
              name: assistant-service-config
              key: ASPNETCORE_URLS
        - name: OLLAMA_URL
          valueFrom:
            secretKeyRef:
              name: assistant-service-secret
              key: OLLAMA_URL
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 180
          periodSeconds: 45
          timeoutSeconds: 30
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
---
apiVersion: v1
kind: Service
metadata:
  name: assistant-service
  namespace: abraapi
  labels:
    app: assistant-service
    service: assistant
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: assistant-service
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: assistant-service-hpa
  namespace: abraapi
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: assistant-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
