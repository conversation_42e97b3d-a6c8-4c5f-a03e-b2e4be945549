# Simple K3s Setup for AbraAPI

No scripts needed - just simple commands and manifests.

## 1. Install K3s

```bash
curl -sfL https://get.k3s.io | sh -
sudo chmod 644 /etc/rancher/k3s/k3s.yaml
export KUBECONFIG=/etc/rancher/k3s/k3s.yaml
```

## 2. Create Namespaces

```bash
kubectl create namespace abraapi
kubectl create namespace abraapi-infrastructure
```

## 3. Deploy Infrastructure

### PostgreSQL
```bash
kubectl apply -f infrastructure/postgres/postgres.yml
```

### Redis
```bash
kubectl apply -f infrastructure/redis/redis.yml
```

Wait for them to be ready:
```bash
kubectl wait --for=condition=ready pod -l app=postgres -n abraapi-infrastructure --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n abraapi-infrastructure --timeout=300s
```

## 4. Update Secrets

Edit the secret files and replace the base64 values with your actual credentials:

```bash
# Get base64 encoded values
echo -n "your_actual_value" | base64

# Edit secrets
kubectl edit secret assistant-service-secret -n abraapi
kubectl edit secret auth-service-secret -n abraapi
kubectl edit secret marketdata-service-secret -n abraapi
kubectl edit secret thread-service-secret -n abraapi
```

## 5. Deploy Services

Deploy each service:

```bash
kubectl apply -f services/assistant-service/deployment.yml
kubectl apply -f services/auth-service/deployment.yml
kubectl apply -f services/marketdata-service/deployment.yml
kubectl apply -f services/thread-service/deployment.yml
```

## 6. Check Status

```bash
kubectl get pods -n abraapi
kubectl get services -n abraapi
```

## 7. Local Development

For local development, just use the individual Docker Compose files:

```bash
cd docker-compose
cp .env.example .env
# Edit .env with your values

# Run individual services
docker-compose -f assistant-service.yml up
docker-compose -f auth-service.yml up
docker-compose -f marketdata-service.yml up
docker-compose -f thread-service.yml up
```

## 8. Build and Deploy Updates

When you make changes:

```bash
# Build locally
docker build -t assistant-service:latest ../AssistantService/

# Import to K3s
sudo k3s ctr images import <(docker save assistant-service:latest)

# Restart deployment
kubectl rollout restart deployment/assistant-service -n abraapi
```

That's it! No complex scripts needed.
