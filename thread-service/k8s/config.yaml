apiVersion: v1
kind: Secret
metadata:
  name: thread-service-secret
  namespace: abraapi
type: Opaque
data:
  # Base64 encoded values - update these with your actual credentials
  # echo -n "your_supabase_connection_string" | base64
  SUPABASE_CONNECTION_STRING: eW91cl9zdXBhYmFzZV9jb25uZWN0aW9uX3N0cmluZw==
  # echo -n "your_supabase_url" | base64
  SUPABASE_URL: eW91cl9zdXBhYmFzZV91cmw=
  # echo -n "your_supabase_jwt_secret" | base64
  SUPABASE_JWT_SECRET: eW91cl9zdXBhYmFzZV9qd3Rfc2VjcmV0
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: thread-service-config
  namespace: abraapi
data:
  ASPNETCORE_ENVIRONMENT: "Production"
  ASPNETCORE_URLS: "http://+:8080"
