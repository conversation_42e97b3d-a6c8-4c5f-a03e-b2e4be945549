# Abra API

A microservices-based backend system for financial market data and AI assistant capabilities.

![.NET](https://img.shields.io/badge/.NET-10.0-512BD4)
![Docker](https://img.shields.io/badge/Docker-Enabled-2496ED)
![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15-336791)
![Redis](https://img.shields.io/badge/Redis-7-DC382D)

## Overview

Abra API is a collection of microservices built with .NET 10.0 that provides:

- **MarketDataService**: Financial market data aggregation from multiple brokers (Finnhub, Polygon) with Redis caching and PostgreSQL storage
- **AssistantService**: AI-powered assistant capabilities with Ollama integration, featuring content filtering and rate limiting
- **AuthService**: User authentication and profile management via Supabase with OAuth support
- **ThreadService**: Basic thread/post management service (development)

The system is designed to be deployed as a cohesive unit using Docker Compose, with <PERSON>inx as a reverse proxy providing API gateway functionality.

## Architecture

The repository follows a microservices architecture with each service in its own directory containing embedded Kubernetes configurations:

```
abraapp/
├── auth-service/
│   ├── Dockerfile
│   ├── AuthService.csproj
│   ├── Controllers/
│   ├── Services/
│   ├── Models/
│   └── k8s/
│       ├── deployment.yaml
│       ├── service.yaml
│       ├── ingress.yaml
│       └── config.yaml
├── marketdata-service/
│   ├── Dockerfile
│   ├── MarketDataService.csproj
│   ├── Controllers/
│   ├── Services/
│   ├── Models/
│   └── k8s/
│       ├── deployment.yaml
│       ├── service.yaml
│       ├── ingress.yaml
│       └── config.yaml
├── assistant-service/
│   ├── Dockerfile
│   ├── AssistantService.csproj
│   ├── Controllers/
│   ├── Services/
│   ├── Models/
│   └── k8s/
│       ├── deployment.yaml
│       ├── service.yaml
│       ├── ingress.yaml
│       └── config.yaml
├── thread-service/
│   ├── Dockerfile
│   ├── ThreadService.csproj
│   ├── Controllers/
│   ├── Services/
│   ├── Models/
│   └── k8s/
│       ├── deployment.yaml
│       ├── service.yaml
│       ├── ingress.yaml
│       └── config.yaml
├── infrastructure/
│   └── k8s/
│       ├── namespace.yml
│       ├── nginx/
│       ├── postgres/
│       └── redis/
├── nginx/                 # Nginx reverse proxy configuration
├── tests/                 # Test projects for all services
└── docker-compose.yml     # Container orchestration
```

### Service Details

- **Nginx Reverse Proxy**: API gateway providing routing, rate limiting (10 req/s), security headers, and load balancing
- **MarketDataService**: Aggregates financial data from Finnhub and Polygon APIs, with Redis caching and PostgreSQL storage for watchlists
- **AssistantService**: AI assistant with Ollama integration, featuring content filtering, prompt injection detection, and rate limiting
- **AuthService**: User authentication and profile management using Supabase with OAuth support and JWT tokens
- **ThreadService**: Social media functionality with posts, comments, likes, and user interactions

## Prerequisites

- [.NET 10.0 SDK](https://dotnet.microsoft.com/download/dotnet/10.0)
- [Docker](https://www.docker.com/products/docker-desktop)
- [Docker Compose](https://docs.docker.com/compose/install/)
- [PostgreSQL 15](https://www.postgresql.org/download/) (for local development)
- [Redis 7](https://redis.io/download) (for local development)

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```bash
# Database Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=marketdata
REDIS_CONNECTION_STRING=redis:6379

# API Keys
FINNHUB_API_KEY=your_finnhub_api_key
POLYGON_API_KEY=your_polygon_api_key

# Supabase Configuration
SUPABASE_JWT_SECRET=your_supabase_jwt_secret
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SUPABASE_PROJECT_ID=your_supabase_project_id

# Ollama Configuration
OLLAMA_URL=http://assistant.undeclab.com/api/generate

# Environment
ASPNETCORE_ENVIRONMENT=Production
```

## Getting Started

### Running with Docker Compose

The easiest way to run the entire system is using Docker Compose:

```bash
# Build and start all services
docker compose up --build

# Run in detached mode
docker compose up -d --build

# Stop all services
docker compose down
```

### Running Individual Services

Each service can be run independently for development:

```bash
# Assistant Service
dotnet run --project AssistantService/AssistantService.csproj

# Auth Service
dotnet run --project AuthService/AuthService.csproj

# Market Data Service
dotnet run --project MarketDataService/MarketDataService.csproj

# Thread Service (development)
dotnet run --project ThreadService/ThreadService.csproj
```

### Building the Solution

```bash
# Build entire solution
dotnet build abraapi.sln

# Restore packages
dotnet restore abraapi.sln
```

## Testing

The repository includes comprehensive test suites for all services:

```bash
# Run all tests
dotnet test abraapi.sln

# Run tests for specific service
dotnet test tests/auth-service.Tests/auth-service.Tests.csproj
dotnet test tests/assistant-service.Tests/assistant-service.Tests.csproj
dotnet test tests/marketdata-service.Tests/marketdata-service.Tests.csproj

# Run specific test categories
dotnet test --filter Category=Unit
dotnet test --filter Category=Integration
dotnet test --filter Category=E2E

# Run tests with coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Test Structure
- `tests/auth-service.Tests/` - Authentication service tests
- `tests/assistant-service.Tests/` - AI assistant service tests  
- `tests/marketdata-service.Tests/` - Market data service tests

## API Endpoints

All services are accessible through the nginx reverse proxy on port 80:

### MarketDataService
- `GET /api/brokers` - List available brokers and metadata
- `GET /api/marketdata/{symbol}` - Get current market data for a symbol
- `GET /api/watchlist` - Get user watchlists (requires authentication)
- `POST /api/watchlist` - Create a new watchlist (requires authentication)
- `PUT /api/watchlist/{id}` - Update watchlist (requires authentication)
- `DELETE /api/watchlist/{id}` - Delete watchlist (requires authentication)

### AssistantService
- `POST /api/assistant/ask` - Ask a question to the AI assistant
- `POST /api/chat` - Send a message to the chat system (supports streaming)

**Features:**
- Content filtering and prompt injection detection
- Rate limiting per user
- Streaming response support
- Security middleware

### AuthService

#### Authentication Endpoints
- `POST /api/auth/signup` - User signup with email/password
- `POST /api/auth/login` - User login with email/password
- `POST /api/auth/token/refresh` - Refresh access token
- `POST /api/auth/recover` - Send password recovery email
- `POST /api/auth/otp` - Send OTP via email or SMS
- `POST /api/auth/verify` - Verify OTP or email confirmation
- `POST /api/auth/logout` - User logout
- `GET /api/auth/settings` - Get authentication settings

#### User Management Endpoints
- `GET /api/auth/user` - Get current authenticated user
- `PUT /api/auth/user` - Update user information
- `GET /api/auth/profile` - Get user profile from database
- `POST /api/auth/profile/link-broker` - Link broker to user profile

#### OAuth Endpoints
- `POST /api/auth/oauth` - Get OAuth authorization URL
- `POST /api/auth/oauth/callback` - Handle OAuth callback and exchange code for tokens

### ThreadService

#### Post Management
- `GET /api/posts` - Get all posts with pagination
- `GET /api/posts/{id}` - Get specific post by ID
- `POST /api/posts` - Create a new post (requires authentication)
- `PUT /api/posts/{id}` - Update post (requires authentication)
- `DELETE /api/posts/{id}` - Delete post (requires authentication)

#### Comment Management
- `GET /api/comments/post/{postId}` - Get comments for a specific post
- `POST /api/comments` - Create a new comment (requires authentication)
- `PUT /api/comments/{id}` - Update comment (requires authentication)
- `DELETE /api/comments/{id}` - Delete comment (requires authentication)

#### Like/Share System
- `POST /api/likes` - Like/unlike a post or comment (requires authentication)
- `GET /api/likes/post/{postId}` - Get likes for a specific post
- `GET /api/likes/user/{userId}` - Get user's liked content

**Features:**
- JWT authentication integration
- PostgreSQL storage for posts and interactions
- Memory caching for performance
- User profile integration

### Gateway Endpoints
- `GET /health` - Health check endpoint
- `GET /` - API information and available services

### Rate Limiting
- API endpoints: 10 requests/second (burst: 20-50 depending on endpoint)
- Assistant endpoints: Extended timeout (300s) for AI processing
- Market data endpoints: Higher burst limit (50) for data-intensive operations
- Thread endpoints: Medium burst limit (30) for social interactions

## Deployment

The repository includes GitHub Actions workflows for automated CI/CD deployment to Google Compute Engine.

### Deployment Process

The deployment pipeline (`.github/workflows/ci-cd.yml`) automatically:

1. **Triggers**: On push to `main` branch or manual workflow dispatch
2. **Fast Deployment**: Optimized 15-minute deployment process
3. **Steps**:
   - Pulls latest code from main branch
   - Creates environment configuration from GitHub secrets
   - Builds Docker images with BuildKit optimization
   - Deploys services with health checks
   - Runs optimized container orchestration

### Production Environment

- **Platform**: Google Compute Engine
- **Orchestration**: Docker Compose with health checks
- **Proxy**: Nginx with SSL termination
- **Database**: PostgreSQL 15 with persistent volumes
- **Cache**: Redis 7 with data persistence
- **Monitoring**: Health checks for all services

### Environment Secrets

Configure the following secrets in GitHub repository settings:
- Database credentials (`POSTGRES_USER`, `POSTGRES_PASSWORD`, `POSTGRES_DB`)
- API keys (`FINNHUB_API_KEY`, `POLYGON_API_KEY`)
- Supabase configuration (`SUPABASE_*`)
- SSH deployment credentials (`SSH_HOST`, `SSH_USERNAME`, `SSH_PRIVATE_KEY`)

## Development

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd abraapi
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start dependencies** (PostgreSQL and Redis)
   ```bash
   docker compose up postgres redis -d
   ```

4. **Run services individually**
   ```bash
   dotnet run --project MarketDataService/MarketDataService.csproj
   dotnet run --project AuthService/AuthService.csproj
   dotnet run --project AssistantService/AssistantService.csproj
   ```

### Service Ports (Development)
- **MarketDataService**: `https://localhost:7001`, `http://localhost:5001`
- **AuthService**: `https://localhost:7002`, `http://localhost:5002`  
- **AssistantService**: `https://localhost:7003`, `http://localhost:5003`
- **ThreadService**: `https://localhost:7004`, `http://localhost:5004`

### HTTP Test Files
Each service includes `.http` files for testing endpoints:
- `MarketDataService/MarketDataService.http`
- `AuthService/AuthService.http`
- `AssistantService/AssistantService.http`
- `AssistantService/test-endpoints.http`
- `AssistantService/test-prompt-injection.http`

## Technology Stack

### Backend
- **.NET 10.0** - Primary framework
- **ASP.NET Core** - Web API framework
- **Entity Framework Core** - ORM for database operations
- **PostgreSQL 15** - Primary database
- **Redis 7** - Caching and session storage

### External Services
- **Supabase** - Authentication and user management
- **Ollama** - AI model hosting and inference
- **Finnhub API** - Financial market data
- **Polygon API** - Financial market data

### Infrastructure
- **Docker & Docker Compose** - Containerization
- **Nginx** - Reverse proxy and load balancer
- **GitHub Actions** - CI/CD pipeline
- **Google Compute Engine** - Production hosting

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes and add tests
4. Ensure all tests pass (`dotnet test`)
5. Commit your changes (`git commit -m 'Add some amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Code Style
- Follow standard C# coding conventions
- Use meaningful variable and method names
- Add XML documentation for public APIs
- Include unit tests for new functionality

## License

Proprietary - All rights reserved
