services:
  # Database services - start first
  postgres:
    image: postgres:15-alpine
    container_name: postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - pgdata_market:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis:
    image: redis:7-alpine
    container_name: redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  # Application services - optimized for parallel builds
  assistant-service:
    container_name: assistant-service
    restart: unless-stopped
    build:
      context: ./assistant-service
      dockerfile: Dockerfile
      target: runtime
      args:
        BUILDKIT_INLINE_CACHE: 1
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - OLLAMA_URL=${OLLAMA_URL:-http://assistant.undeclab.com/api/generate}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 45s
      timeout: 30s
      retries: 5
      start_period: 180s

  auth-service:
    container_name: auth-service
    restart: unless-stopped
    build:
      context: ./auth-service
      dockerfile: Dockerfile
      target: runtime
      args:
        BUILDKIT_INLINE_CACHE: 1
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_PROJECT_ID=${SUPABASE_PROJECT_ID}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 45s
      timeout: 30s
      retries: 5
      start_period: 180s

  marketdata-service:
    container_name: marketdata-service
    restart: unless-stopped
    build:
      context: ./marketdata-service
      dockerfile: Dockerfile
      target: runtime
      args:
        BUILDKIT_INLINE_CACHE: 1
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - SUPABASE_CONNECTION_STRING=${SUPABASE_CONNECTION_STRING}
      - Redis__ConnectionString=${REDIS_CONNECTION_STRING}
      - FINNHUB_API_KEY=${FINNHUB_API_KEY}
      - POLYGON_API_KEY=${POLYGON_API_KEY}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 45s
      timeout: 30s
      retries: 5
      start_period: 180s

  thread-service:
    container_name: thread-service
    restart: unless-stopped
    build:
      context: ./thread-service
      dockerfile: Dockerfile
      target: runtime
      args:
        BUILDKIT_INLINE_CACHE: 1
    expose:
      - "8080"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:8080
      - SUPABASE_CONNECTION_STRING=${SUPABASE_CONNECTION_STRING}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health/ready"]
      interval: 45s
      timeout: 30s
      retries: 5
      start_period: 300s

  # Nginx proxy - start last
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      assistant-service:
        condition: service_healthy
      auth-service:
        condition: service_healthy
      marketdata-service:
        condition: service_healthy
      thread-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  pgdata_market:
  redis_data:
  nginx_logs: