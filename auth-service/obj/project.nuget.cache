{"version": 2, "dgSpecHash": "CtGsRvoduVA=", "success": true, "projectFilePath": "/home/<USER>/devWorks/undecProjects/abraapi/AuthService/AuthService.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/microsoft.aspnetcore.authentication.jwtbearer/8.0.11/microsoft.aspnetcore.authentication.jwtbearer.8.0.11.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.aspnetcore.openapi/8.0.0/microsoft.aspnetcore.openapi.8.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.apidescription.server/6.0.5/microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.caching.memory/8.0.1/microsoft.extensions.caching.memory.8.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.identitymodel.abstractions/8.12.1/microsoft.identitymodel.abstractions.8.12.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.identitymodel.jsonwebtokens/8.12.1/microsoft.identitymodel.jsonwebtokens.8.12.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.identitymodel.logging/8.12.1/microsoft.identitymodel.logging.8.12.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.identitymodel.protocols/7.1.2/microsoft.identitymodel.protocols.7.1.2.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.identitymodel.protocols.openidconnect/7.1.2/microsoft.identitymodel.protocols.openidconnect.7.1.2.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.identitymodel.tokens/8.12.1/microsoft.identitymodel.tokens.8.12.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.io.recyclablememorystream/3.0.0/microsoft.io.recyclablememorystream.3.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.openapi/1.4.3/microsoft.openapi.1.4.3.nupkg.sha512", "/home/<USER>/.nuget/packages/mimemapping/3.0.1/mimemapping.3.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/newtonsoft.json/13.0.3/newtonsoft.json.13.0.3.nupkg.sha512", "/home/<USER>/.nuget/packages/supabase/1.1.1/supabase.1.1.1.nupkg.sha512", "/home/<USER>/.nuget/packages/supabase.core/1.0.0/supabase.core.1.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/supabase.functions/2.0.0/supabase.functions.2.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/supabase.gotrue/6.0.3/supabase.gotrue.6.0.3.nupkg.sha512", "/home/<USER>/.nuget/packages/supabase.postgrest/4.0.3/supabase.postgrest.4.0.3.nupkg.sha512", "/home/<USER>/.nuget/packages/supabase.realtime/7.0.2/supabase.realtime.7.0.2.nupkg.sha512", "/home/<USER>/.nuget/packages/supabase.storage/2.0.2/supabase.storage.2.0.2.nupkg.sha512", "/home/<USER>/.nuget/packages/swashbuckle.aspnetcore/6.5.0/swashbuckle.aspnetcore.6.5.0.nupkg.sha512", "/home/<USER>/.nuget/packages/swashbuckle.aspnetcore.swagger/6.5.0/swashbuckle.aspnetcore.swagger.6.5.0.nupkg.sha512", "/home/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggergen/6.5.0/swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg.sha512", "/home/<USER>/.nuget/packages/swashbuckle.aspnetcore.swaggerui/6.5.0/swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.identitymodel.tokens.jwt/8.12.1/system.identitymodel.tokens.jwt.8.12.1.nupkg.sha512", "/home/<USER>/.nuget/packages/system.reactive/6.0.0/system.reactive.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/websocket.client/5.1.1/websocket.client.5.1.1.nupkg.sha512"], "logs": [{"code": "NU1510", "level": "Warning", "message": "PackageReference Microsoft.Extensions.Caching.Memory will not be pruned. Consider removing this package from your dependencies, as it is likely unnecessary.", "projectPath": "/home/<USER>/devWorks/undecProjects/abraapi/AuthService/AuthService.csproj", "warningLevel": 1, "filePath": "/home/<USER>/devWorks/undecProjects/abraapi/AuthService/AuthService.csproj", "libraryId": "Microsoft.Extensions.Caching.Memory", "targetGraphs": ["net10.0"]}]}