{"version": 3, "targets": {"net10.0": {"Microsoft.AspNetCore.OpenApi/9.0.0": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.6.17"}, "compile": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "build": {"build/Microsoft.Extensions.ApiDescription.Server.props": {}, "build/Microsoft.Extensions.ApiDescription.Server.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props": {}, "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets": {}}}, "Microsoft.OpenApi/1.6.17": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore/6.4.0": {"type": "package", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.4.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.4.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.4.0"}, "build": {"build/Swashbuckle.AspNetCore.props": {}}}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.2.3"}, "compile": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"type": "package", "dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.4.0"}, "compile": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"related": ".pdb;.xml"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"type": "package", "compile": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}}}, "libraries": {"Microsoft.AspNetCore.OpenApi/9.0.0": {"sha512": "FqUK5j1EOPNuFT7IafltZQ3cakqhSwVzH5ZW1MhZDe4pPXs9sJ2M5jom1Omsu+mwF2tNKKlRAzLRHQTZzbd+6Q==", "type": "package", "path": "microsoft.aspnetcore.openapi/9.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "lib/net9.0/Microsoft.AspNetCore.OpenApi.dll", "lib/net9.0/Microsoft.AspNetCore.OpenApi.xml", "microsoft.aspnetcore.openapi.9.0.0.nupkg.sha512", "microsoft.aspnetcore.openapi.nuspec"]}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"sha512": "Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "type": "package", "path": "microsoft.extensions.apidescription.server/6.0.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/Microsoft.Extensions.ApiDescription.Server.props", "build/Microsoft.Extensions.ApiDescription.Server.targets", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets", "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "microsoft.extensions.apidescription.server.nuspec", "tools/Newtonsoft.Json.dll", "tools/dotnet-getdocument.deps.json", "tools/dotnet-getdocument.dll", "tools/dotnet-getdocument.runtimeconfig.json", "tools/net461-x86/GetDocument.Insider.exe", "tools/net461-x86/GetDocument.Insider.exe.config", "tools/net461-x86/Microsoft.Win32.Primitives.dll", "tools/net461-x86/System.AppContext.dll", "tools/net461-x86/System.Buffers.dll", "tools/net461-x86/System.Collections.Concurrent.dll", "tools/net461-x86/System.Collections.NonGeneric.dll", "tools/net461-x86/System.Collections.Specialized.dll", "tools/net461-x86/System.Collections.dll", "tools/net461-x86/System.ComponentModel.EventBasedAsync.dll", "tools/net461-x86/System.ComponentModel.Primitives.dll", "tools/net461-x86/System.ComponentModel.TypeConverter.dll", "tools/net461-x86/System.ComponentModel.dll", "tools/net461-x86/System.Console.dll", "tools/net461-x86/System.Data.Common.dll", "tools/net461-x86/System.Diagnostics.Contracts.dll", "tools/net461-x86/System.Diagnostics.Debug.dll", "tools/net461-x86/System.Diagnostics.DiagnosticSource.dll", "tools/net461-x86/System.Diagnostics.FileVersionInfo.dll", "tools/net461-x86/System.Diagnostics.Process.dll", "tools/net461-x86/System.Diagnostics.StackTrace.dll", "tools/net461-x86/System.Diagnostics.TextWriterTraceListener.dll", "tools/net461-x86/System.Diagnostics.Tools.dll", "tools/net461-x86/System.Diagnostics.TraceSource.dll", "tools/net461-x86/System.Diagnostics.Tracing.dll", "tools/net461-x86/System.Drawing.Primitives.dll", "tools/net461-x86/System.Dynamic.Runtime.dll", "tools/net461-x86/System.Globalization.Calendars.dll", "tools/net461-x86/System.Globalization.Extensions.dll", "tools/net461-x86/System.Globalization.dll", "tools/net461-x86/System.IO.Compression.ZipFile.dll", "tools/net461-x86/System.IO.Compression.dll", "tools/net461-x86/System.IO.FileSystem.DriveInfo.dll", "tools/net461-x86/System.IO.FileSystem.Primitives.dll", "tools/net461-x86/System.IO.FileSystem.Watcher.dll", "tools/net461-x86/System.IO.FileSystem.dll", "tools/net461-x86/System.IO.IsolatedStorage.dll", "tools/net461-x86/System.IO.MemoryMappedFiles.dll", "tools/net461-x86/System.IO.Pipes.dll", "tools/net461-x86/System.IO.UnmanagedMemoryStream.dll", "tools/net461-x86/System.IO.dll", "tools/net461-x86/System.Linq.Expressions.dll", "tools/net461-x86/System.Linq.Parallel.dll", "tools/net461-x86/System.Linq.Queryable.dll", "tools/net461-x86/System.Linq.dll", "tools/net461-x86/System.Memory.dll", "tools/net461-x86/System.Net.Http.dll", "tools/net461-x86/System.Net.NameResolution.dll", "tools/net461-x86/System.Net.NetworkInformation.dll", "tools/net461-x86/System.Net.Ping.dll", "tools/net461-x86/System.Net.Primitives.dll", "tools/net461-x86/System.Net.Requests.dll", "tools/net461-x86/System.Net.Security.dll", "tools/net461-x86/System.Net.Sockets.dll", "tools/net461-x86/System.Net.WebHeaderCollection.dll", "tools/net461-x86/System.Net.WebSockets.Client.dll", "tools/net461-x86/System.Net.WebSockets.dll", "tools/net461-x86/System.Numerics.Vectors.dll", "tools/net461-x86/System.ObjectModel.dll", "tools/net461-x86/System.Reflection.Extensions.dll", "tools/net461-x86/System.Reflection.Primitives.dll", "tools/net461-x86/System.Reflection.dll", "tools/net461-x86/System.Resources.Reader.dll", "tools/net461-x86/System.Resources.ResourceManager.dll", "tools/net461-x86/System.Resources.Writer.dll", "tools/net461-x86/System.Runtime.CompilerServices.Unsafe.dll", "tools/net461-x86/System.Runtime.CompilerServices.VisualC.dll", "tools/net461-x86/System.Runtime.Extensions.dll", "tools/net461-x86/System.Runtime.Handles.dll", "tools/net461-x86/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net461-x86/System.Runtime.InteropServices.dll", "tools/net461-x86/System.Runtime.Numerics.dll", "tools/net461-x86/System.Runtime.Serialization.Formatters.dll", "tools/net461-x86/System.Runtime.Serialization.Json.dll", "tools/net461-x86/System.Runtime.Serialization.Primitives.dll", "tools/net461-x86/System.Runtime.Serialization.Xml.dll", "tools/net461-x86/System.Runtime.dll", "tools/net461-x86/System.Security.Claims.dll", "tools/net461-x86/System.Security.Cryptography.Algorithms.dll", "tools/net461-x86/System.Security.Cryptography.Csp.dll", "tools/net461-x86/System.Security.Cryptography.Encoding.dll", "tools/net461-x86/System.Security.Cryptography.Primitives.dll", "tools/net461-x86/System.Security.Cryptography.X509Certificates.dll", "tools/net461-x86/System.Security.Principal.dll", "tools/net461-x86/System.Security.SecureString.dll", "tools/net461-x86/System.Text.Encoding.Extensions.dll", "tools/net461-x86/System.Text.Encoding.dll", "tools/net461-x86/System.Text.RegularExpressions.dll", "tools/net461-x86/System.Threading.Overlapped.dll", "tools/net461-x86/System.Threading.Tasks.Parallel.dll", "tools/net461-x86/System.Threading.Tasks.dll", "tools/net461-x86/System.Threading.Thread.dll", "tools/net461-x86/System.Threading.ThreadPool.dll", "tools/net461-x86/System.Threading.Timer.dll", "tools/net461-x86/System.Threading.dll", "tools/net461-x86/System.ValueTuple.dll", "tools/net461-x86/System.Xml.ReaderWriter.dll", "tools/net461-x86/System.Xml.XDocument.dll", "tools/net461-x86/System.Xml.XPath.XDocument.dll", "tools/net461-x86/System.Xml.XPath.dll", "tools/net461-x86/System.Xml.XmlDocument.dll", "tools/net461-x86/System.Xml.XmlSerializer.dll", "tools/net461-x86/netstandard.dll", "tools/net461/GetDocument.Insider.exe", "tools/net461/GetDocument.Insider.exe.config", "tools/net461/Microsoft.Win32.Primitives.dll", "tools/net461/System.AppContext.dll", "tools/net461/System.Buffers.dll", "tools/net461/System.Collections.Concurrent.dll", "tools/net461/System.Collections.NonGeneric.dll", "tools/net461/System.Collections.Specialized.dll", "tools/net461/System.Collections.dll", "tools/net461/System.ComponentModel.EventBasedAsync.dll", "tools/net461/System.ComponentModel.Primitives.dll", "tools/net461/System.ComponentModel.TypeConverter.dll", "tools/net461/System.ComponentModel.dll", "tools/net461/System.Console.dll", "tools/net461/System.Data.Common.dll", "tools/net461/System.Diagnostics.Contracts.dll", "tools/net461/System.Diagnostics.Debug.dll", "tools/net461/System.Diagnostics.DiagnosticSource.dll", "tools/net461/System.Diagnostics.FileVersionInfo.dll", "tools/net461/System.Diagnostics.Process.dll", "tools/net461/System.Diagnostics.StackTrace.dll", "tools/net461/System.Diagnostics.TextWriterTraceListener.dll", "tools/net461/System.Diagnostics.Tools.dll", "tools/net461/System.Diagnostics.TraceSource.dll", "tools/net461/System.Diagnostics.Tracing.dll", "tools/net461/System.Drawing.Primitives.dll", "tools/net461/System.Dynamic.Runtime.dll", "tools/net461/System.Globalization.Calendars.dll", "tools/net461/System.Globalization.Extensions.dll", "tools/net461/System.Globalization.dll", "tools/net461/System.IO.Compression.ZipFile.dll", "tools/net461/System.IO.Compression.dll", "tools/net461/System.IO.FileSystem.DriveInfo.dll", "tools/net461/System.IO.FileSystem.Primitives.dll", "tools/net461/System.IO.FileSystem.Watcher.dll", "tools/net461/System.IO.FileSystem.dll", "tools/net461/System.IO.IsolatedStorage.dll", "tools/net461/System.IO.MemoryMappedFiles.dll", "tools/net461/System.IO.Pipes.dll", "tools/net461/System.IO.UnmanagedMemoryStream.dll", "tools/net461/System.IO.dll", "tools/net461/System.Linq.Expressions.dll", "tools/net461/System.Linq.Parallel.dll", "tools/net461/System.Linq.Queryable.dll", "tools/net461/System.Linq.dll", "tools/net461/System.Memory.dll", "tools/net461/System.Net.Http.dll", "tools/net461/System.Net.NameResolution.dll", "tools/net461/System.Net.NetworkInformation.dll", "tools/net461/System.Net.Ping.dll", "tools/net461/System.Net.Primitives.dll", "tools/net461/System.Net.Requests.dll", "tools/net461/System.Net.Security.dll", "tools/net461/System.Net.Sockets.dll", "tools/net461/System.Net.WebHeaderCollection.dll", "tools/net461/System.Net.WebSockets.Client.dll", "tools/net461/System.Net.WebSockets.dll", "tools/net461/System.Numerics.Vectors.dll", "tools/net461/System.ObjectModel.dll", "tools/net461/System.Reflection.Extensions.dll", "tools/net461/System.Reflection.Primitives.dll", "tools/net461/System.Reflection.dll", "tools/net461/System.Resources.Reader.dll", "tools/net461/System.Resources.ResourceManager.dll", "tools/net461/System.Resources.Writer.dll", "tools/net461/System.Runtime.CompilerServices.Unsafe.dll", "tools/net461/System.Runtime.CompilerServices.VisualC.dll", "tools/net461/System.Runtime.Extensions.dll", "tools/net461/System.Runtime.Handles.dll", "tools/net461/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/net461/System.Runtime.InteropServices.dll", "tools/net461/System.Runtime.Numerics.dll", "tools/net461/System.Runtime.Serialization.Formatters.dll", "tools/net461/System.Runtime.Serialization.Json.dll", "tools/net461/System.Runtime.Serialization.Primitives.dll", "tools/net461/System.Runtime.Serialization.Xml.dll", "tools/net461/System.Runtime.dll", "tools/net461/System.Security.Claims.dll", "tools/net461/System.Security.Cryptography.Algorithms.dll", "tools/net461/System.Security.Cryptography.Csp.dll", "tools/net461/System.Security.Cryptography.Encoding.dll", "tools/net461/System.Security.Cryptography.Primitives.dll", "tools/net461/System.Security.Cryptography.X509Certificates.dll", "tools/net461/System.Security.Principal.dll", "tools/net461/System.Security.SecureString.dll", "tools/net461/System.Text.Encoding.Extensions.dll", "tools/net461/System.Text.Encoding.dll", "tools/net461/System.Text.RegularExpressions.dll", "tools/net461/System.Threading.Overlapped.dll", "tools/net461/System.Threading.Tasks.Parallel.dll", "tools/net461/System.Threading.Tasks.dll", "tools/net461/System.Threading.Thread.dll", "tools/net461/System.Threading.ThreadPool.dll", "tools/net461/System.Threading.Timer.dll", "tools/net461/System.Threading.dll", "tools/net461/System.ValueTuple.dll", "tools/net461/System.Xml.ReaderWriter.dll", "tools/net461/System.Xml.XDocument.dll", "tools/net461/System.Xml.XPath.XDocument.dll", "tools/net461/System.Xml.XPath.dll", "tools/net461/System.Xml.XmlDocument.dll", "tools/net461/System.Xml.XmlSerializer.dll", "tools/net461/netstandard.dll", "tools/netcoreapp2.1/GetDocument.Insider.deps.json", "tools/netcoreapp2.1/GetDocument.Insider.dll", "tools/netcoreapp2.1/GetDocument.Insider.runtimeconfig.json", "tools/netcoreapp2.1/System.Diagnostics.DiagnosticSource.dll"]}, "Microsoft.OpenApi/1.6.17": {"sha512": "Le+kehlmrlQfuDFUt1zZ2dVwrhFQtKREdKBo+rexOwaCoYP0/qpgT9tLxCsZjsgR5Itk1UKPcbgO+FyaNid/bA==", "type": "package", "path": "microsoft.openapi/1.6.17", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.6.17.nupkg.sha512", "microsoft.openapi.nuspec"]}, "Swashbuckle.AspNetCore/6.4.0": {"sha512": "eUBr4TW0up6oKDA5Xwkul289uqSMgY0xGN4pnbOIBqCcN9VKGGaPvHX3vWaG/hvocfGDP+MGzMA0bBBKz2fkmQ==", "type": "package", "path": "swashbuckle.aspnetcore/6.4.0", "files": [".nupkg.metadata", ".signature.p7s", "build/Swashbuckle.AspNetCore.props", "swashbuckle.aspnetcore.6.4.0.nupkg.sha512", "swashbuckle.aspnetcore.nuspec"]}, "Swashbuckle.AspNetCore.Swagger/6.4.0": {"sha512": "nl4SBgGM+cmthUcpwO/w1lUjevdDHAqRvfUoe4Xp/Uvuzt9mzGUwyFCqa3ODBAcZYBiFoKvrYwz0rabslJvSmQ==", "type": "package", "path": "swashbuckle.aspnetcore.swagger/6.4.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.xml", "swashbuckle.aspnetcore.swagger.6.4.0.nupkg.sha512", "swashbuckle.aspnetcore.swagger.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerGen/6.4.0": {"sha512": "lXhcUBVqKrPFAQF7e/ZeDfb5PMgE8n5t6L5B6/BQSpiwxgHzmBcx8Msu42zLYFTvR5PIqE9Q9lZvSQAcwCxJjw==", "type": "package", "path": "swashbuckle.aspnetcore.swaggergen/6.4.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "swashbuckle.aspnetcore.swaggergen.6.4.0.nupkg.sha512", "swashbuckle.aspnetcore.swaggergen.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerUI/6.4.0": {"sha512": "1Hh3atb3pi8c+v7n4/3N80Jj8RvLOXgWxzix6w3OZhB7zBGRwsy7FWr4e3hwgPweSBpwfElqj4V4nkjYabH9nQ==", "type": "package", "path": "swashbuckle.aspnetcore.swaggerui/6.4.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "swashbuckle.aspnetcore.swaggerui.6.4.0.nupkg.sha512", "swashbuckle.aspnetcore.swaggerui.nuspec"]}}, "projectFileDependencyGroups": {"net10.0": ["Microsoft.AspNetCore.OpenApi >= 9.0.0", "Swashbuckle.AspNetCore >= 6.4.0"]}, "packageFolders": {"/home/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/devWorks/undecProjects/abraapi/AssistantService/AssistantService.csproj", "projectName": "AssistantService", "projectPath": "/home/<USER>/devWorks/undecProjects/abraapi/AssistantService/AssistantService.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/devWorks/undecProjects/abraapi/AssistantService/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net10.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net10.0": {"targetAlias": "net10.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net10.0": {"targetAlias": "net10.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/home/<USER>/.dotnet/sdk/10.0.100-preview.6.25358.103/PortableRuntimeIdentifierGraph.json", "packagesToPrune": {"Microsoft.AspNetCore": "(,10.0.32767]", "Microsoft.AspNetCore.Antiforgery": "(,10.0.32767]", "Microsoft.AspNetCore.App": "(,10.0.32767]", "Microsoft.AspNetCore.Authentication": "(,10.0.32767]", "Microsoft.AspNetCore.Authentication.Abstractions": "(,10.0.32767]", "Microsoft.AspNetCore.Authentication.BearerToken": "(,10.0.32767]", "Microsoft.AspNetCore.Authentication.Cookies": "(,10.0.32767]", "Microsoft.AspNetCore.Authentication.Core": "(,10.0.32767]", "Microsoft.AspNetCore.Authentication.OAuth": "(,10.0.32767]", "Microsoft.AspNetCore.Authorization": "(,10.0.32767]", "Microsoft.AspNetCore.Authorization.Policy": "(,10.0.32767]", "Microsoft.AspNetCore.Components": "(,10.0.32767]", "Microsoft.AspNetCore.Components.Authorization": "(,10.0.32767]", "Microsoft.AspNetCore.Components.Endpoints": "(,10.0.32767]", "Microsoft.AspNetCore.Components.Forms": "(,10.0.32767]", "Microsoft.AspNetCore.Components.Server": "(,10.0.32767]", "Microsoft.AspNetCore.Components.Web": "(,10.0.32767]", "Microsoft.AspNetCore.Connections.Abstractions": "(,10.0.32767]", "Microsoft.AspNetCore.CookiePolicy": "(,10.0.32767]", "Microsoft.AspNetCore.Cors": "(,10.0.32767]", "Microsoft.AspNetCore.Cryptography.Internal": "(,10.0.32767]", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "(,10.0.32767]", "Microsoft.AspNetCore.DataProtection": "(,10.0.32767]", "Microsoft.AspNetCore.DataProtection.Abstractions": "(,10.0.32767]", "Microsoft.AspNetCore.DataProtection.Extensions": "(,10.0.32767]", "Microsoft.AspNetCore.Diagnostics": "(,10.0.32767]", "Microsoft.AspNetCore.Diagnostics.Abstractions": "(,10.0.32767]", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "(,10.0.32767]", "Microsoft.AspNetCore.HostFiltering": "(,10.0.32767]", "Microsoft.AspNetCore.Hosting": "(,10.0.32767]", "Microsoft.AspNetCore.Hosting.Abstractions": "(,10.0.32767]", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "(,10.0.32767]", "Microsoft.AspNetCore.Html.Abstractions": "(,10.0.32767]", "Microsoft.AspNetCore.Http": "(,10.0.32767]", "Microsoft.AspNetCore.Http.Abstractions": "(,10.0.32767]", "Microsoft.AspNetCore.Http.Connections": "(,10.0.32767]", "Microsoft.AspNetCore.Http.Connections.Common": "(,10.0.32767]", "Microsoft.AspNetCore.Http.Extensions": "(,10.0.32767]", "Microsoft.AspNetCore.Http.Features": "(,10.0.32767]", "Microsoft.AspNetCore.Http.Results": "(,10.0.32767]", "Microsoft.AspNetCore.HttpLogging": "(,10.0.32767]", "Microsoft.AspNetCore.HttpOverrides": "(,10.0.32767]", "Microsoft.AspNetCore.HttpsPolicy": "(,10.0.32767]", "Microsoft.AspNetCore.Identity": "(,10.0.32767]", "Microsoft.AspNetCore.Localization": "(,10.0.32767]", "Microsoft.AspNetCore.Localization.Routing": "(,10.0.32767]", "Microsoft.AspNetCore.Metadata": "(,10.0.32767]", "Microsoft.AspNetCore.Mvc": "(,10.0.32767]", "Microsoft.AspNetCore.Mvc.Abstractions": "(,10.0.32767]", "Microsoft.AspNetCore.Mvc.ApiExplorer": "(,10.0.32767]", "Microsoft.AspNetCore.Mvc.Core": "(,10.0.32767]", "Microsoft.AspNetCore.Mvc.Cors": "(,10.0.32767]", "Microsoft.AspNetCore.Mvc.DataAnnotations": "(,10.0.32767]", "Microsoft.AspNetCore.Mvc.Formatters.Json": "(,10.0.32767]", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "(,10.0.32767]", "Microsoft.AspNetCore.Mvc.Localization": "(,10.0.32767]", "Microsoft.AspNetCore.Mvc.Razor": "(,10.0.32767]", "Microsoft.AspNetCore.Mvc.RazorPages": "(,10.0.32767]", "Microsoft.AspNetCore.Mvc.TagHelpers": "(,10.0.32767]", "Microsoft.AspNetCore.Mvc.ViewFeatures": "(,10.0.32767]", "Microsoft.AspNetCore.OutputCaching": "(,10.0.32767]", "Microsoft.AspNetCore.RateLimiting": "(,10.0.32767]", "Microsoft.AspNetCore.Razor": "(,10.0.32767]", "Microsoft.AspNetCore.Razor.Runtime": "(,10.0.32767]", "Microsoft.AspNetCore.RequestDecompression": "(,10.0.32767]", "Microsoft.AspNetCore.ResponseCaching": "(,10.0.32767]", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "(,10.0.32767]", "Microsoft.AspNetCore.ResponseCompression": "(,10.0.32767]", "Microsoft.AspNetCore.Rewrite": "(,10.0.32767]", "Microsoft.AspNetCore.Routing": "(,10.0.32767]", "Microsoft.AspNetCore.Routing.Abstractions": "(,10.0.32767]", "Microsoft.AspNetCore.Server.HttpSys": "(,10.0.32767]", "Microsoft.AspNetCore.Server.IIS": "(,10.0.32767]", "Microsoft.AspNetCore.Server.IISIntegration": "(,10.0.32767]", "Microsoft.AspNetCore.Server.Kestrel": "(,10.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Core": "(,10.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes": "(,10.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic": "(,10.0.32767]", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "(,10.0.32767]", "Microsoft.AspNetCore.Session": "(,10.0.32767]", "Microsoft.AspNetCore.SignalR": "(,10.0.32767]", "Microsoft.AspNetCore.SignalR.Common": "(,10.0.32767]", "Microsoft.AspNetCore.SignalR.Core": "(,10.0.32767]", "Microsoft.AspNetCore.SignalR.Protocols.Json": "(,10.0.32767]", "Microsoft.AspNetCore.StaticAssets": "(,10.0.32767]", "Microsoft.AspNetCore.StaticFiles": "(,10.0.32767]", "Microsoft.AspNetCore.WebSockets": "(,10.0.32767]", "Microsoft.AspNetCore.WebUtilities": "(,10.0.32767]", "Microsoft.CSharp": "(,4.7.32767]", "Microsoft.Extensions.Caching.Abstractions": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Caching.Memory": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.Abstractions": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.Binder": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.CommandLine": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.EnvironmentVariables": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.FileExtensions": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.Ini": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.Json": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.KeyPerFile": "(,10.0.32767]", "Microsoft.Extensions.Configuration.UserSecrets": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Configuration.Xml": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.DependencyInjection": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.DependencyInjection.Abstractions": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Diagnostics": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Diagnostics.Abstractions": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Diagnostics.HealthChecks": "(,10.0.32767]", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "(,10.0.32767]", "Microsoft.Extensions.Features": "(,10.0.32767]", "Microsoft.Extensions.FileProviders.Abstractions": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.FileProviders.Composite": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.FileProviders.Embedded": "(,10.0.32767]", "Microsoft.Extensions.FileProviders.Physical": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.FileSystemGlobbing": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Hosting": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Hosting.Abstractions": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Http": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Identity.Core": "(,10.0.32767]", "Microsoft.Extensions.Identity.Stores": "(,10.0.32767]", "Microsoft.Extensions.Localization": "(,10.0.32767]", "Microsoft.Extensions.Localization.Abstractions": "(,10.0.32767]", "Microsoft.Extensions.Logging": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.Abstractions": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.Configuration": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.Console": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.Debug": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.EventLog": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.EventSource": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Logging.TraceSource": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.ObjectPool": "(,10.0.32767]", "Microsoft.Extensions.Options": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Options.ConfigurationExtensions": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Options.DataAnnotations": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Primitives": "(,10.0.0-preview.6.25358.103]", "Microsoft.Extensions.Validation": "(,10.0.32767]", "Microsoft.Extensions.WebEncoders": "(,10.0.32767]", "Microsoft.JSInterop": "(,10.0.32767]", "Microsoft.Net.Http.Headers": "(,10.0.32767]", "Microsoft.VisualBasic": "(,10.4.32767]", "Microsoft.Win32.Primitives": "(,4.3.32767]", "Microsoft.Win32.Registry": "(,5.0.32767]", "runtime.any.System.Collections": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.any.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.any.System.Globalization": "(,4.3.32767]", "runtime.any.System.Globalization.Calendars": "(,4.3.32767]", "runtime.any.System.IO": "(,4.3.32767]", "runtime.any.System.Reflection": "(,4.3.32767]", "runtime.any.System.Reflection.Extensions": "(,4.3.32767]", "runtime.any.System.Reflection.Primitives": "(,4.3.32767]", "runtime.any.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.any.System.Runtime": "(,4.3.32767]", "runtime.any.System.Runtime.Handles": "(,4.3.32767]", "runtime.any.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.any.System.Text.Encoding": "(,4.3.32767]", "runtime.any.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.any.System.Threading.Tasks": "(,4.3.32767]", "runtime.any.System.Threading.Timer": "(,4.3.32767]", "runtime.aot.System.Collections": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tools": "(,4.3.32767]", "runtime.aot.System.Diagnostics.Tracing": "(,4.3.32767]", "runtime.aot.System.Globalization": "(,4.3.32767]", "runtime.aot.System.Globalization.Calendars": "(,4.3.32767]", "runtime.aot.System.IO": "(,4.3.32767]", "runtime.aot.System.Reflection": "(,4.3.32767]", "runtime.aot.System.Reflection.Extensions": "(,4.3.32767]", "runtime.aot.System.Reflection.Primitives": "(,4.3.32767]", "runtime.aot.System.Resources.ResourceManager": "(,4.3.32767]", "runtime.aot.System.Runtime": "(,4.3.32767]", "runtime.aot.System.Runtime.Handles": "(,4.3.32767]", "runtime.aot.System.Runtime.InteropServices": "(,4.3.32767]", "runtime.aot.System.Text.Encoding": "(,4.3.32767]", "runtime.aot.System.Text.Encoding.Extensions": "(,4.3.32767]", "runtime.aot.System.Threading.Tasks": "(,4.3.32767]", "runtime.aot.System.Threading.Timer": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.debian.9-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.27-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.fedora.28-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.opensuse.42.3-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "(,4.3.32767]", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography": "(,4.3.32767]", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Http": "(,4.3.32767]", "runtime.ubuntu.18.04-x64.runtime.native.System.Net.Security": "(,4.3.32767]", "runtime.unix.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.unix.System.Console": "(,4.3.32767]", "runtime.unix.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.unix.System.IO.FileSystem": "(,4.3.32767]", "runtime.unix.System.Net.Primitives": "(,4.3.32767]", "runtime.unix.System.Net.Sockets": "(,4.3.32767]", "runtime.unix.System.Private.Uri": "(,4.3.32767]", "runtime.unix.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win.Microsoft.Win32.Primitives": "(,4.3.32767]", "runtime.win.System.Console": "(,4.3.32767]", "runtime.win.System.Diagnostics.Debug": "(,4.3.32767]", "runtime.win.System.IO.FileSystem": "(,4.3.32767]", "runtime.win.System.Net.Primitives": "(,4.3.32767]", "runtime.win.System.Net.Sockets": "(,4.3.32767]", "runtime.win.System.Runtime.Extensions": "(,4.3.32767]", "runtime.win10-arm-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-arm64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win10-x64-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win10-x86-aot.runtime.native.System.IO.Compression": "(,4.0.32767]", "runtime.win7-x64.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7-x86.runtime.native.System.IO.Compression": "(,4.3.32767]", "runtime.win7.System.Private.Uri": "(,4.3.32767]", "runtime.win8-arm.runtime.native.System.IO.Compression": "(,4.3.32767]", "System.AppContext": "(,4.3.32767]", "System.Buffers": "(,5.0.32767]", "System.Collections": "(,4.3.32767]", "System.Collections.Concurrent": "(,4.3.32767]", "System.Collections.Immutable": "(,10.0.0-preview.6.25358.103]", "System.Collections.NonGeneric": "(,4.3.32767]", "System.Collections.Specialized": "(,4.3.32767]", "System.ComponentModel": "(,4.3.32767]", "System.ComponentModel.Annotations": "(,4.3.32767]", "System.ComponentModel.EventBasedAsync": "(,4.3.32767]", "System.ComponentModel.Primitives": "(,4.3.32767]", "System.ComponentModel.TypeConverter": "(,4.3.32767]", "System.Console": "(,4.3.32767]", "System.Data.Common": "(,4.3.32767]", "System.Data.DataSetExtensions": "(,4.4.32767]", "System.Diagnostics.Contracts": "(,4.3.32767]", "System.Diagnostics.Debug": "(,4.3.32767]", "System.Diagnostics.DiagnosticSource": "(,10.0.0-preview.6.25358.103]", "System.Diagnostics.EventLog": "(,10.0.0-preview.6.25358.103]", "System.Diagnostics.FileVersionInfo": "(,4.3.32767]", "System.Diagnostics.Process": "(,4.3.32767]", "System.Diagnostics.StackTrace": "(,4.3.32767]", "System.Diagnostics.TextWriterTraceListener": "(,4.3.32767]", "System.Diagnostics.Tools": "(,4.3.32767]", "System.Diagnostics.TraceSource": "(,4.3.32767]", "System.Diagnostics.Tracing": "(,4.3.32767]", "System.Drawing.Primitives": "(,4.3.32767]", "System.Dynamic.Runtime": "(,4.3.32767]", "System.Formats.Asn1": "(,10.0.0-preview.6.25358.103]", "System.Formats.Cbor": "(,10.0.0-preview.6.25358.103]", "System.Formats.Tar": "(,10.0.0-preview.6.25358.103]", "System.Globalization": "(,4.3.32767]", "System.Globalization.Calendars": "(,4.3.32767]", "System.Globalization.Extensions": "(,4.3.32767]", "System.IO": "(,4.3.32767]", "System.IO.Compression": "(,4.3.32767]", "System.IO.Compression.ZipFile": "(,4.3.32767]", "System.IO.FileSystem": "(,4.3.32767]", "System.IO.FileSystem.AccessControl": "(,4.4.32767]", "System.IO.FileSystem.DriveInfo": "(,4.3.32767]", "System.IO.FileSystem.Primitives": "(,4.3.32767]", "System.IO.FileSystem.Watcher": "(,4.3.32767]", "System.IO.IsolatedStorage": "(,4.3.32767]", "System.IO.MemoryMappedFiles": "(,4.3.32767]", "System.IO.Pipelines": "(,10.0.0-preview.6.25358.103]", "System.IO.Pipes": "(,4.3.32767]", "System.IO.Pipes.AccessControl": "(,5.0.32767]", "System.IO.UnmanagedMemoryStream": "(,4.3.32767]", "System.Linq": "(,4.3.32767]", "System.Linq.AsyncEnumerable": "(,10.0.0-preview.6.25358.103]", "System.Linq.Expressions": "(,4.3.32767]", "System.Linq.Parallel": "(,4.3.32767]", "System.Linq.Queryable": "(,4.3.32767]", "System.Memory": "(,5.0.32767]", "System.Net.Http": "(,4.3.32767]", "System.Net.Http.Json": "(,10.0.0-preview.6.25358.103]", "System.Net.NameResolution": "(,4.3.32767]", "System.Net.NetworkInformation": "(,4.3.32767]", "System.Net.Ping": "(,4.3.32767]", "System.Net.Primitives": "(,4.3.32767]", "System.Net.Requests": "(,4.3.32767]", "System.Net.Security": "(,4.3.32767]", "System.Net.ServerSentEvents": "(,10.0.0-preview.6.25358.103]", "System.Net.Sockets": "(,4.3.32767]", "System.Net.WebHeaderCollection": "(,4.3.32767]", "System.Net.WebSockets": "(,4.3.32767]", "System.Net.WebSockets.Client": "(,4.3.32767]", "System.Numerics.Vectors": "(,5.0.32767]", "System.ObjectModel": "(,4.3.32767]", "System.Private.DataContractSerialization": "(,4.3.32767]", "System.Private.Uri": "(,4.3.32767]", "System.Reflection": "(,4.3.32767]", "System.Reflection.DispatchProxy": "(,6.0.32767]", "System.Reflection.Emit": "(,4.7.32767]", "System.Reflection.Emit.ILGeneration": "(,4.7.32767]", "System.Reflection.Emit.Lightweight": "(,4.7.32767]", "System.Reflection.Extensions": "(,4.3.32767]", "System.Reflection.Metadata": "(,10.0.0-preview.6.25358.103]", "System.Reflection.Primitives": "(,4.3.32767]", "System.Reflection.TypeExtensions": "(,4.3.32767]", "System.Resources.Reader": "(,4.3.32767]", "System.Resources.ResourceManager": "(,4.3.32767]", "System.Resources.Writer": "(,4.3.32767]", "System.Runtime": "(,4.3.32767]", "System.Runtime.CompilerServices.Unsafe": "(,7.0.32767]", "System.Runtime.CompilerServices.VisualC": "(,4.3.32767]", "System.Runtime.Extensions": "(,4.3.32767]", "System.Runtime.Handles": "(,4.3.32767]", "System.Runtime.InteropServices": "(,4.3.32767]", "System.Runtime.InteropServices.RuntimeInformation": "(,4.3.32767]", "System.Runtime.Loader": "(,4.3.32767]", "System.Runtime.Numerics": "(,4.3.32767]", "System.Runtime.Serialization.Formatters": "(,4.3.32767]", "System.Runtime.Serialization.Json": "(,4.3.32767]", "System.Runtime.Serialization.Primitives": "(,4.3.32767]", "System.Runtime.Serialization.Xml": "(,4.3.32767]", "System.Security.AccessControl": "(,6.0.32767]", "System.Security.Claims": "(,4.3.32767]", "System.Security.Cryptography.Algorithms": "(,4.3.32767]", "System.Security.Cryptography.Cng": "(,5.0.32767]", "System.Security.Cryptography.Csp": "(,4.3.32767]", "System.Security.Cryptography.Encoding": "(,4.3.32767]", "System.Security.Cryptography.OpenSsl": "(,5.0.32767]", "System.Security.Cryptography.Primitives": "(,4.3.32767]", "System.Security.Cryptography.X509Certificates": "(,4.3.32767]", "System.Security.Cryptography.Xml": "(,10.0.0-preview.6.25358.103]", "System.Security.Principal": "(,4.3.32767]", "System.Security.Principal.Windows": "(,5.0.32767]", "System.Security.SecureString": "(,4.3.32767]", "System.Text.Encoding": "(,4.3.32767]", "System.Text.Encoding.CodePages": "(,10.0.0-preview.6.25358.103]", "System.Text.Encoding.Extensions": "(,4.3.32767]", "System.Text.Encodings.Web": "(,10.0.0-preview.6.25358.103]", "System.Text.Json": "(,10.0.0-preview.6.25358.103]", "System.Text.RegularExpressions": "(,4.3.32767]", "System.Threading": "(,4.3.32767]", "System.Threading.AccessControl": "(,10.0.0-preview.6.25358.103]", "System.Threading.Channels": "(,10.0.0-preview.6.25358.103]", "System.Threading.Overlapped": "(,4.3.32767]", "System.Threading.RateLimiting": "(,10.0.0-preview.6.25358.103]", "System.Threading.Tasks": "(,4.3.32767]", "System.Threading.Tasks.Dataflow": "(,10.0.0-preview.6.25358.103]", "System.Threading.Tasks.Extensions": "(,5.0.32767]", "System.Threading.Tasks.Parallel": "(,4.3.32767]", "System.Threading.Thread": "(,4.3.32767]", "System.Threading.ThreadPool": "(,4.3.32767]", "System.Threading.Timer": "(,4.3.32767]", "System.ValueTuple": "(,4.5.32767]", "System.Xml.ReaderWriter": "(,4.3.32767]", "System.Xml.XDocument": "(,4.3.32767]", "System.Xml.XmlDocument": "(,4.3.32767]", "System.Xml.XmlSerializer": "(,4.3.32767]", "System.Xml.XPath": "(,4.3.32767]", "System.Xml.XPath.XDocument": "(,5.0.32767]"}}}}}